// 创建正确的密码哈希
const bcrypt = require('bcryptjs');

async function createPasswordHash() {
  const password = 'password';
  const saltRounds = 10;
  
  try {
    const hash = await bcrypt.hash(password, saltRounds);
    console.log('密码:', password);
    console.log('哈希:', hash);
    
    // 验证哈希是否正确
    const isValid = await bcrypt.compare(password, hash);
    console.log('验证结果:', isValid);
    
  } catch (error) {
    console.error('生成哈希失败:', error);
  }
}

createPasswordHash();