#!/bin/bash

# 手动恢复Docker项目指南脚本
# 使用方法: ./manual-recovery-guide.sh [项目名称]

PROJECT_NAME=${1:-"all"}

echo "🔧 Docker项目手动恢复指南"
echo "=========================="

# 项目列表
declare -A PROJECTS=(
    ["erp-claudecode"]="/Users/<USER>/Desktop/0607ERP-claudecode"
    ["erp-cursor"]="/Users/<USER>/Desktop/0608ERP-2-Cursor"
    ["tanka"]="/Users/<USER>/Desktop/0615tanka"
    ["jsherp"]="/Users/<USER>/Desktop/0619jshERP"
    ["linghua-erp-0605"]="/Users/<USER>/Desktop/LinghuaERP0605"
    ["jsherp-cursor"]="/Users/<USER>/Desktop/jshERP-0612-Cursor"
    ["linghua-erp-v0-0609"]="/Users/<USER>/Desktop/linghua-erp-v0-0609"
    ["linghua-erp-v0"]="/Users/<USER>/Desktop/linghua-erp-v0"
    ["vue-vben-admin"]="/Users/<USER>/Desktop/vue-vben-admin-main"
)

# 恢复单个项目的函数
recover_project() {
    local name=$1
    local path=$2
    
    echo ""
    echo "🚀 恢复项目: $name"
    echo "📁 路径: $path"
    echo "---"
    
    if [ ! -d "$path" ]; then
        echo "❌ 项目目录不存在: $path"
        return 1
    fi
    
    cd "$path" || return 1
    
    # 检查Docker配置文件
    if [ -f "docker-compose.yml" ]; then
        echo "✅ 发现 docker-compose.yml"
        
        # 显示当前状态
        echo "📊 当前容器状态:"
        docker-compose ps 2>/dev/null || echo "   无运行中的容器"
        
        echo ""
        echo "🔧 执行恢复命令:"
        echo "   cd $path"
        echo "   docker-compose down"
        echo "   docker-compose up -d"
        echo ""
        
        # 询问是否执行
        read -p "是否立即执行恢复? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "⏳ 停止现有服务..."
            docker-compose down
            
            echo "🚀 启动服务..."
            if docker-compose up -d; then
                echo "✅ $name 恢复成功!"
                echo "📊 服务状态:"
                docker-compose ps
            else
                echo "❌ $name 恢复失败，可能需要手动检查配置"
            fi
        else
            echo "⏭️ 跳过自动恢复，请手动执行上述命令"
        fi
        
    elif [ -f "Dockerfile" ]; then
        echo "✅ 发现 Dockerfile (需要手动配置docker-compose)"
        echo "💡 建议: 检查是否有其他Docker配置文件"
    else
        echo "❌ 未发现Docker配置文件"
    fi
    
    echo "=================================="
}

# 快速检查所有项目状态
quick_check() {
    echo "🔍 快速检查所有项目状态:"
    echo ""
    
    for name in "${!PROJECTS[@]}"; do
        path="${PROJECTS[$name]}"
        if [ -d "$path" ]; then
            cd "$path"
            if [ -f "docker-compose.yml" ]; then
                echo "📁 $name: ✅ 有Docker配置"
                # 检查是否有运行中的容器
                running=$(docker-compose ps -q 2>/dev/null | wc -l)
                if [ "$running" -gt 0 ]; then
                    echo "   🟢 有 $running 个容器运行中"
                else
                    echo "   🔴 无运行中的容器"
                fi
            else
                echo "📁 $name: ❌ 无Docker配置"
            fi
        else
            echo "📁 $name: ❌ 目录不存在"
        fi
    done
}

# 主逻辑
if [ "$PROJECT_NAME" = "all" ]; then
    quick_check
    echo ""
    echo "💡 使用方法:"
    echo "   $0 [项目名称]  # 恢复特定项目"
    echo "   $0 quick       # 快速检查状态"
    echo ""
    echo "📋 可用项目:"
    for name in "${!PROJECTS[@]}"; do
        echo "   - $name"
    done
elif [ "$PROJECT_NAME" = "quick" ]; then
    quick_check
elif [ -n "${PROJECTS[$PROJECT_NAME]}" ]; then
    recover_project "$PROJECT_NAME" "${PROJECTS[$PROJECT_NAME]}"
else
    echo "❌ 未知项目: $PROJECT_NAME"
    echo "📋 可用项目:"
    for name in "${!PROJECTS[@]}"; do
        echo "   - $name"
    done
fi
