#!/bin/bash

# Docker项目状态检查脚本

echo "🔍 Docker项目状态检查报告"
echo "================================"
echo ""

# 检查当前运行的容器
echo "📊 当前运行的Docker容器："
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""

# 检查所有容器（包括停止的）
echo "📋 所有Docker容器状态："
docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
echo ""

# 检查Docker镜像
echo "🖼️ 可用的Docker镜像："
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" | head -20
echo ""

# 检查Docker网络
echo "🌐 Docker网络："
docker network ls
echo ""

# 检查Docker卷
echo "💾 Docker数据卷："
docker volume ls
echo ""

# 统计信息
echo "📈 统计信息："
echo "   运行中容器: $(docker ps -q | wc -l)"
echo "   总容器数: $(docker ps -a -q | wc -l)"
echo "   镜像数量: $(docker images -q | wc -l)"
echo "   网络数量: $(docker network ls -q | wc -l)"
echo "   数据卷数量: $(docker volume ls -q | wc -l)"
echo ""

echo "✅ 状态检查完成！"
