#!/bin/bash

# Docker项目恢复脚本
# 用于恢复被误删的其他Docker项目

echo "🔧 开始恢复其他Docker项目..."

# 项目路径数组 - 完整列表
PROJECTS=(
    "/Users/<USER>/Desktop/0607ERP-claudecode"
    "/Users/<USER>/Desktop/0607linghua-enamel-gallery"
    "/Users/<USER>/Desktop/0607linghua-enamel-gallery_副本"
    "/Users/<USER>/Desktop/0608ERP-2-Cursor"
    "/Users/<USER>/Desktop/0608ERP-2-Cursor_副本"
    "/Users/<USER>/Desktop/0611jshERP-Q"
    "/Users/<USER>/Desktop/0611jshERP-master"
    "/Users/<USER>/Desktop/0615tanka"
    "/Users/<USER>/Desktop/0619jshERP"
    "/Users/<USER>/Desktop/LinghuaERP0605"
    "/Users/<USER>/Desktop/jshERP-0612-Cursor"
    "/Users/<USER>/Desktop/jshERP-current-backup-20250616_114803"
    "/Users/<USER>/Desktop/linghua-erp-v0-0609"
    "/Users/<USER>/Desktop/linghua-erp-v0"
    "/Users/<USER>/Desktop/vue-vben-admin-main"
)

# 恢复函数
restore_project() {
    local project_path=$1
    local project_name=$(basename "$project_path")
    
    echo "📁 检查项目: $project_name"
    
    if [ -d "$project_path" ]; then
        echo "✅ 项目目录存在: $project_path"
        
        # 检查是否有docker-compose文件
        if [ -f "$project_path/docker-compose.yml" ] || [ -f "$project_path/docker-compose.yaml" ]; then
            echo "🐳 发现Docker Compose配置，尝试启动..."
            cd "$project_path"
            
            # 尝试启动服务
            if docker-compose up -d 2>/dev/null; then
                echo "✅ $project_name 启动成功"
                docker-compose ps
            else
                echo "⚠️ $project_name 启动失败，可能需要手动检查"
            fi
            echo "---"
        else
            echo "ℹ️ $project_name 没有Docker Compose配置"
        fi
    else
        echo "❌ 项目目录不存在: $project_path"
    fi
}

# 遍历所有项目
for project in "${PROJECTS[@]}"; do
    restore_project "$project"
done

echo ""
echo "🎉 恢复脚本执行完成！"
echo ""
echo "📋 手动检查建议："
echo "1. 检查各项目的docker-compose.ps输出"
echo "2. 验证服务端口是否正常"
echo "3. 检查数据是否完整"
echo ""
echo "🔧 如果某个项目启动失败，请手动进入目录执行："
echo "   cd [项目目录]"
echo "   docker-compose up -d"
