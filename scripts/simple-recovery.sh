#!/bin/bash

# 简单的Docker项目恢复脚本

echo "🔧 Docker项目恢复工具"
echo "===================="

# 项目目录列表
PROJECTS=(
    "/Users/<USER>/Desktop/0607ERP-claudecode"
    "/Users/<USER>/Desktop/0608ERP-2-Cursor"
    "/Users/<USER>/Desktop/0615tanka"
    "/Users/<USER>/Desktop/0619jshERP"
    "/Users/<USER>/Desktop/LinghuaERP0605"
    "/Users/<USER>/Desktop/jshERP-0612-Cursor"
    "/Users/<USER>/Desktop/linghua-erp-v0-0609"
    "/Users/<USER>/Desktop/linghua-erp-v0"
    "/Users/<USER>/Desktop/vue-vben-admin-main"
)

# 检查项目状态
check_projects() {
    echo "🔍 检查所有Docker项目状态:"
    echo ""
    
    for project_path in "${PROJECTS[@]}"; do
        project_name=$(basename "$project_path")
        echo "📁 检查项目: $project_name"
        
        if [ -d "$project_path" ]; then
            echo "   ✅ 目录存在: $project_path"
            
            if [ -f "$project_path/docker-compose.yml" ]; then
                echo "   🐳 有Docker Compose配置"
                
                # 检查容器状态
                cd "$project_path"
                running_containers=$(docker-compose ps -q 2>/dev/null | wc -l | tr -d ' ')
                if [ "$running_containers" -gt 0 ]; then
                    echo "   🟢 有 $running_containers 个容器运行中"
                    docker-compose ps
                else
                    echo "   🔴 无运行中的容器"
                fi
            else
                echo "   ❌ 无docker-compose.yml文件"
            fi
        else
            echo "   ❌ 目录不存在"
        fi
        echo "   ---"
    done
}

# 恢复特定项目
recover_project() {
    local project_path=$1
    local project_name=$(basename "$project_path")
    
    echo "🚀 恢复项目: $project_name"
    echo "📁 路径: $project_path"
    
    if [ ! -d "$project_path" ]; then
        echo "❌ 项目目录不存在"
        return 1
    fi
    
    if [ ! -f "$project_path/docker-compose.yml" ]; then
        echo "❌ 无docker-compose.yml文件"
        return 1
    fi
    
    cd "$project_path"
    
    echo "⏳ 停止现有服务..."
    docker-compose down
    
    echo "🚀 启动服务..."
    if docker-compose up -d; then
        echo "✅ $project_name 启动成功!"
        echo "📊 服务状态:"
        docker-compose ps
    else
        echo "❌ $project_name 启动失败"
        echo "💡 建议手动检查配置文件"
    fi
}

# 批量恢复所有项目
recover_all() {
    echo "🚀 开始批量恢复所有项目..."
    
    for project_path in "${PROJECTS[@]}"; do
        if [ -d "$project_path" ] && [ -f "$project_path/docker-compose.yml" ]; then
            echo ""
            echo "=================================="
            recover_project "$project_path"
            echo "=================================="
            sleep 2
        fi
    done
    
    echo ""
    echo "🎉 批量恢复完成!"
}

# 主菜单
show_menu() {
    echo ""
    echo "📋 选择操作:"
    echo "1. 检查所有项目状态"
    echo "2. 批量恢复所有项目"
    echo "3. 恢复特定项目"
    echo "4. 退出"
    echo ""
    read -p "请选择 (1-4): " choice
    
    case $choice in
        1)
            check_projects
            show_menu
            ;;
        2)
            recover_all
            show_menu
            ;;
        3)
            echo ""
            echo "📋 可用项目:"
            for i in "${!PROJECTS[@]}"; do
                echo "   $((i+1)). $(basename "${PROJECTS[$i]}")"
            done
            echo ""
            read -p "请选择项目编号: " project_num
            
            if [ "$project_num" -ge 1 ] && [ "$project_num" -le "${#PROJECTS[@]}" ]; then
                recover_project "${PROJECTS[$((project_num-1))]}"
            else
                echo "❌ 无效的项目编号"
            fi
            show_menu
            ;;
        4)
            echo "👋 再见!"
            exit 0
            ;;
        *)
            echo "❌ 无效选择"
            show_menu
            ;;
    esac
}

# 如果有参数，直接执行对应操作
if [ "$1" = "check" ]; then
    check_projects
elif [ "$1" = "recover-all" ]; then
    recover_all
else
    show_menu
fi
