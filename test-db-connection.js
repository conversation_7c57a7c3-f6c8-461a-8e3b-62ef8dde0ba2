const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 测试查询
    const result = await prisma.$queryRaw`SELECT version()`;
    console.log('✅ 数据库查询成功:', result);
    
    await prisma.$disconnect();
    process.exit(0);
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('详细错误:', error);
    process.exit(1);
  }
}

testConnection();