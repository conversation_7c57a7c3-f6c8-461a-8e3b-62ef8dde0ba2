// 简化版创建管理员脚本
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createAdmin() {
  try {
    console.log('开始创建管理员账户...');

    // 创建管理员用户（使用明文密码，仅用于测试）
    const adminUser = await prisma.user.create({
      data: {
        name: '管理员',
        email: '<EMAIL>',
        password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // 这是 'password' 的bcrypt哈希
        role: 'admin',
      },
    });

    console.log('管理员账户创建成功！');
    console.log('邮箱: <EMAIL>');
    console.log('密码: password');
    console.log('用户ID:', adminUser.id);

  } catch (error) {
    if (error.code === 'P2002') {
      console.log('用户已存在，尝试更新...');
      
      const updatedUser = await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          name: '管理员',
          password: '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
          role: 'admin',
        },
      });
      
      console.log('管理员账户更新成功！');
      console.log('邮箱: <EMAIL>');
      console.log('密码: password');
    } else {
      console.error('创建管理员账户时出错:', error);
    }
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin();