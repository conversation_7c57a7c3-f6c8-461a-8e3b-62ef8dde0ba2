#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

console.log('🔍 数据库连接诊断工具');
console.log('=========================\n');

// 安全地显示连接字符串（隐藏密码）
function maskConnectionString(connectionString) {
  if (!connectionString) return 'undefined';
  return connectionString.replace(/:([^:@]+)@/, ':***@');
}

// 解析连接字符串
function parseConnectionString(connectionString) {
  if (!connectionString) return null;
  
  try {
    const url = new URL(connectionString);
    return {
      protocol: url.protocol,
      hostname: url.hostname,
      port: url.port || '5432',
      database: url.pathname.slice(1),
      username: url.username,
      schema: url.searchParams.get('schema') || 'public'
    };
  } catch (error) {
    return { error: error.message };
  }
}

async function checkEnvironmentConfig() {
  console.log('1️⃣ 检查环境配置');
  console.log('─────────────────────\n');
  
  // 检查 .env 文件
  const envPath = path.join(process.cwd(), '.env');
  console.log('📁 .env 文件检查:');
  
  if (fs.existsSync(envPath)) {
    console.log('✅ .env 文件存在');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      if (line.startsWith('DATABASE_URL=')) {
        const dbUrl = line.split('=')[1].replace(/"/g, '');
        console.log(`   DATABASE_URL: ${maskConnectionString(dbUrl)}`);
        
        const parsed = parseConnectionString(dbUrl);
        if (parsed && !parsed.error) {
          console.log(`   - 主机: ${parsed.hostname}:${parsed.port}`);
          console.log(`   - 数据库: ${parsed.database}`);
          console.log(`   - 用户: ${parsed.username}`);
          console.log(`   - Schema: ${parsed.schema}`);
        }
      }
    });
  } else {
    console.log('❌ .env 文件不存在');
  }
  
  // 检查环境变量
  console.log('\n🌍 环境变量检查:');
  const envDbUrl = process.env.DATABASE_URL;
  if (envDbUrl) {
    console.log(`✅ DATABASE_URL 环境变量存在: ${maskConnectionString(envDbUrl)}`);
    
    const parsed = parseConnectionString(envDbUrl);
    if (parsed && !parsed.error) {
      console.log(`   - 主机: ${parsed.hostname}:${parsed.port}`);
      console.log(`   - 数据库: ${parsed.database}`);
      console.log(`   - 用户: ${parsed.username}`);
      console.log(`   - Schema: ${parsed.schema}`);
    }
  } else {
    console.log('❌ DATABASE_URL 环境变量未设置');
  }
  
  // 检查其他可能的配置文件
  const configFiles = ['.env.local', '.env.development', '.env.production'];
  console.log('\n📋 其他配置文件检查:');
  
  configFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} 存在`);
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('DATABASE_URL')) {
        console.log(`   ⚠️ ${file} 包含 DATABASE_URL 配置`);
      }
    } else {
      console.log(`❌ ${file} 不存在`);
    }
  });
}

async function testActualConnection() {
  console.log('\n2️⃣ 测试实际数据库连接');
  console.log('─────────────────────────\n');
  
  const prisma = new PrismaClient();
  
  try {
    console.log('🔌 尝试连接数据库...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 获取数据库信息
    console.log('\n📊 数据库信息:');
    try {
      const result = await prisma.$queryRaw`
        SELECT 
          current_database() as database_name,
          current_user as current_user,
          version() as version,
          current_schema() as current_schema
      `;
      
      if (result && result.length > 0) {
        const info = result[0];
        console.log(`   - 当前数据库: ${info.database_name}`);
        console.log(`   - 当前用户: ${info.current_user}`);
        console.log(`   - 当前 Schema: ${info.current_schema}`);
        console.log(`   - PostgreSQL 版本: ${info.version.split(' ')[0]} ${info.version.split(' ')[1]}`);
      }
    } catch (error) {
      console.log('⚠️ 无法获取数据库详细信息:', error.message);
    }
    
    return true;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.message);
    
    // 分析常见错误
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 可能原因: PostgreSQL 服务未运行');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('💡 可能原因: 目标数据库不存在');
    } else if (error.message.includes('authentication failed')) {
      console.log('💡 可能原因: 用户名或密码错误');
    }
    
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

async function checkDatabaseContent() {
  console.log('\n3️⃣ 检查数据库内容');
  console.log('─────────────────────\n');
  
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    // 列出所有表
    console.log('📋 数据库表列表:');
    try {
      const tables = await prisma.$queryRaw`
        SELECT table_name, table_type
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        ORDER BY table_name
      `;
      
      if (tables.length === 0) {
        console.log('❌ 数据库为空 - 没有找到任何表');
        return { isEmpty: true, tables: [] };
      }
      
      console.log(`✅ 找到 ${tables.length} 个表:`);
      tables.forEach(table => {
        console.log(`   - ${table.table_name} (${table.table_type})`);
      });
      
      // 检查关键表
      console.log('\n🔍 关键表检查:');
      const keyTables = ['Role', 'Permission', 'User', 'Employee', '_prisma_migrations'];
      const tableNames = tables.map(t => t.table_name);
      
      for (const table of keyTables) {
        if (tableNames.includes(table)) {
          try {
            const count = await prisma.$queryRaw`SELECT COUNT(*) as count FROM ${table}`;
            console.log(`✅ ${table}: ${count[0].count} 条记录`);
          } catch (error) {
            console.log(`❌ ${table}: 无法查询 - ${error.message}`);
          }
        } else {
          console.log(`❌ ${table}: 表不存在`);
        }
      }
      
      return { isEmpty: false, tables: tableNames };
      
    } catch (error) {
      console.log('❌ 无法列出数据库表:', error.message);
      return { error: error.message };
    }
    
  } catch (error) {
    console.log('❌ 无法连接到数据库进行内容检查:', error.message);
    return { error: error.message };
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  try {
    console.log('开始数据库连接诊断...\n');
    
    // 1. 检查配置
    await checkEnvironmentConfig();
    
    // 2. 测试连接
    const connected = await testActualConnection();
    
    if (connected) {
      // 3. 检查内容
      const contentResult = await checkDatabaseContent();
      
      console.log('\n📋 诊断总结');
      console.log('═══════════════════\n');
      
      if (contentResult.isEmpty) {
        console.log('🔍 假设验证结果:');
        console.log('   ✅ 假设2: 数据库为空 - 得到证实');
        console.log('   ❓ 假设1: 连接错误数据库 - 需要进一步验证');
        console.log('\n💡 建议: 数据库连接正常但为空，可能需要从备份恢复或应用迁移');
      } else if (contentResult.tables && !contentResult.tables.includes('Role')) {
        console.log('🔍 假设验证结果:');
        console.log('   ✅ 假设2: 数据库缺少关键表 - 得到证实');
        console.log('   ❓ 假设1: 连接错误数据库 - 需要进一步验证');
        console.log('\n💡 建议: 数据库部分填充但缺少 Role 表，可能需要应用迁移或部分恢复');
      } else if (contentResult.tables && contentResult.tables.includes('Role')) {
        console.log('🔍 假设验证结果:');
        console.log('   ❌ 假设2: 数据库缺少表 - 未得到证实');
        console.log('   ✅ 假设1: 可能存在其他连接或配置问题');
        console.log('\n💡 建议: Role 表存在，问题可能在应用层面或 Prisma 客户端配置');
      }
    } else {
      console.log('\n📋 诊断总结');
      console.log('═══════════════════\n');
      console.log('🔍 假设验证结果:');
      console.log('   ✅ 假设1: 数据库连接问题 - 得到证实');
      console.log('   ❓ 假设2: 无法验证，因为无法连接数据库');
      console.log('\n💡 建议: 首先解决数据库连接问题');
    }
    
  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
  }
}

main();
