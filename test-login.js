// 测试登录功能
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testLogin() {
  try {
    console.log('测试登录功能...');

    // 查找用户
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { name: '<EMAIL>' }
        ]
      },
      select: {
        id: true,
        email: true,
        name: true,
        password: true,
        role: true,
        employeeId: true,
        employee: {
          select: {
            name: true,
            position: true
          }
        }
      },
    });

    if (!user || !user.password) {
      console.log('❌ 用户不存在或密码未设置');
      return;
    }

    console.log('✅ 找到用户:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      hasPassword: !!user.password
    });

    console.log('密码哈希:', user.password);

  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin();