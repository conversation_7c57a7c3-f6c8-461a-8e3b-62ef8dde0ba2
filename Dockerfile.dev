# 聆花珐琅馆ERP系统 - 开发环境Dockerfile（性能优化版）
FROM node:18-alpine

# 🚀 性能优化：设置Node.js内存限制
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NODE_ENV=development
ENV NEXTAUTH_DEBUG=false
ENV LOG_LEVEL=warn

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    libc6-compat \
    openssl \
    git

# 复制package文件
COPY package*.json ./
COPY prisma ./prisma/

# 🔧 创建nextjs用户和组
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# 🚀 性能优化：安装依赖时跳过可选依赖
RUN npm ci --legacy-peer-deps --prefer-offline --no-audit

# 复制源代码
COPY . .

# 🔧 设置权限
RUN chown -R nextjs:nodejs /app && \
    chmod -R 755 /app

# 🚀 性能优化：创建缓存目录
RUN mkdir -p .next/cache/webpack && \
    mkdir -p .next/cache/images && \
    chown -R nextjs:nodejs /app/.next

# 暴露端口
EXPOSE 3000

# 🚀 简化的启动脚本
COPY <<EOF /start-dev.sh
#!/bin/sh
echo "🚀 启动开发环境..."

echo "等待数据库启动..."
until npx prisma db push 2>/dev/null; do
  echo "数据库未就绪，等待中..."
  sleep 2
done

echo "✅ 数据库连接成功"
echo "🔧 生成Prisma客户端..."
npx prisma generate

echo "🔥 启动开发服务器..."
exec npm run dev
EOF

RUN chmod +x /start-dev.sh && \
    chown nextjs:nodejs /start-dev.sh

# 切换到nextjs用户
USER nextjs

# 开发环境启动命令
CMD ["/start-dev.sh"]