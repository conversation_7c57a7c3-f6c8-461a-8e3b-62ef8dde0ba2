import NextAuth from "next-auth"
import type { NextAuthConfig } from "next-auth"
import CredentialsProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import prisma from "@/lib/db"

/**
 * 🚀 优化的NextAuth配置 - 解决频繁回调和调试信息问题
 */
export const authConfig: NextAuthConfig = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        identifier: { label: "用户名或邮箱", type: "text" },
        password: { label: "密码", type: "password" },
      },
      async authorize(credentials) {
        // 🎯 优化：减少冗余日志，只在开发环境记录关键信息
        if (process.env.NODE_ENV === "development") {
          console.log("🔐 [认证] 开始认证流程:", credentials?.identifier)
        }

        // 验证凭证完整性
        if (!credentials?.identifier || !credentials?.password) {
          if (process.env.NODE_ENV === "development") {
            console.warn("❌ [认证] 凭证不完整")
          }
          return null
        }

        try {
          // 🎯 优化：只查询必要字段，减少数据库负载
          const user = await prisma.user.findFirst({
            where: {
              OR: [
                { email: credentials.identifier as string },
                { name: credentials.identifier as string }
              ]
            },
            select: {
              id: true,
              email: true,
              name: true,
              password: true,
              role: true,
              employeeId: true,
              employee: {
                select: {
                  name: true,
                  position: true
                }
              }
            },
          })

          if (!user || !user.password) {
            if (process.env.NODE_ENV === "development") {
              console.warn(`❌ [认证] 用户不存在或密码未设置: ${credentials.identifier}`)
            }
            return null
          }

          // 验证密码
          const isPasswordMatch = await bcrypt.compare(
            credentials.password as string,
            user.password
          )

          if (!isPasswordMatch) {
            if (process.env.NODE_ENV === "development") {
              console.warn(`❌ [认证] 密码验证失败: ${user.email}`)
            }
            return null
          }

          // 🚀 异步记录登录历史（不阻塞认证流程）
          recordLoginHistory(user.id, credentials.identifier).catch(err => {
            console.warn('[认证] 记录登录历史失败:', err.message)
          })

          // 🎯 只返回必要的用户信息
          const authUser = {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            employeeId: user.employeeId,
            employeeName: user.employee?.name || null,
            employeePosition: user.employee?.position || null,
          }

          if (process.env.NODE_ENV === "development") {
            console.log("✅ [认证] 认证成功:", user.email)
          }

          return authUser

        } catch (error) {
          console.error("❌ [认证] 认证异常:", error.message)
          return null
        }
      },
    }),
  ],
  
  // 🔄 优化的回调函数 - 减少不必要的处理和日志
  callbacks: {
    async signIn({ user }) {
      const result = !!user
      if (process.env.NODE_ENV === "development" && process.env.NEXTAUTH_DEBUG === "true") {
        console.log("🚪 [认证] 登录回调:", result)
      }
      return result
    },
    
    // 🎫 JWT回调优化 - 最小化处理逻辑
    async jwt({ token, user }) {
      // 只在用户首次登录时更新token
      if (user) {
        token.id = user.id
        token.role = user.role
        token.employeeId = user.employeeId
        token.employeeName = user.employeeName
        token.employeePosition = user.employeePosition
        
        if (process.env.NODE_ENV === "development" && process.env.NEXTAUTH_DEBUG === "true") {
          console.log("🎫 [认证] JWT更新:", { id: token.id, role: token.role })
        }
      }
      
      // 📊 大幅减少日志输出
      if (process.env.NODE_ENV === "development" && process.env.NEXTAUTH_DEBUG === "true") {
        console.log(`🎫 [NextAuth] JWT回调: { hasUser: ${!!user}, tokenId: '${token.id}' }`)
      }
      
      return token
    },
    
    // 📋 Session回调优化 - 直接从token获取信息，避免额外查询
    async session({ session, token }) {
      if (session.user && token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
        session.user.employeeId = token.employeeId as number
        session.user.employeeName = token.employeeName as string
        session.user.employeePosition = token.employeePosition as string
      }
      
      // 📊 减少日志输出
      if (process.env.NODE_ENV === "development" && process.env.NEXTAUTH_DEBUG === "true") {
        console.log(`📋 [NextAuth] Session回调: { hasSession: ${!!session}, tokenId: '${token?.id}' }`)
        console.log(`📋 [NextAuth] Session更新: { id: '${session.user?.id}', email: '${session.user?.email}' }`)
      }
      
      return session
    },
  },
  
  pages: {
    signIn: "/login",
    error: "/auth/error",
  },
  
  // 🔧 会话策略优化 - 延长会话时间，减少重新认证频率
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30天（从24小时延长）
    updateAge: 24 * 60 * 60,   // 24小时更新一次（减少频繁更新）
  },
  
  // 🎫 JWT配置优化
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30天有效期
  },
  
  secret: process.env.NEXTAUTH_SECRET,
  
  // 🐛 禁用调试模式（关键优化）
  debug: false,
  
  trustHost: true,
  useSecureCookies: process.env.NODE_ENV === "production",
  skipCSRFCheck: true, // 临时跳过CSRF检查以解决登录问题
  
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production" ? "__Secure-next-auth.session-token" : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === "production" ? "__Secure-next-auth.callback-url" : "next-auth.callback-url",
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production" ? "__Host-next-auth.csrf-token" : "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  }
}

/**
 * 🚀 异步记录登录历史（不阻塞认证流程）
 */
async function recordLoginHistory(userId: string, identifier: string) {
  try {
    // 简化的登录历史记录，避免数据库模型依赖问题
    console.log(`📝 [登录历史] 用户 ${userId} 通过 ${identifier} 登录成功 - ${new Date().toISOString()}`)
    
    // 如果需要数据库记录，可以在这里添加
    // await prisma.userLoginHistory.create({
    //   data: {
    //     userId,
    //     identifier,
    //     loginTime: new Date(),
    //     status: 'success'
    //   }
    // });
  } catch (error) {
    // 记录错误但不影响登录流程
    console.warn('[认证] 记录登录历史失败:', error.message)
  }
}

const nextAuth = NextAuth(authConfig)

export const { handlers, auth, signIn, signOut } = nextAuth
export default nextAuth
