# 聆花珐琅馆ERP系统 - Docker Compose开发环境
version: '3.8'

services:
  # PostgreSQL数据库服务
  db:
    image: postgres:15-alpine
    container_name: linghua-erp-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: linghua_enamel_gallery
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5555:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups/database/0606erp-backup-20250628_101737/complete_backup.sql:/docker-entrypoint-initdb.d/restore.sql
    networks:
      - linghua-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d linghua_enamel_gallery"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Next.js应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: linghua-erp-app-dev
    restart: unless-stopped
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/linghua_enamel_gallery?schema=public
      - NEXTAUTH_SECRET=linghua-enamel-gallery-secret-key-2024
      - NEXTAUTH_URL=http://localhost:3001
      - AUTH_TRUST_HOST=true
      - AUTH_SECRET=linghua-enamel-gallery-secret-key-2024
    ports:
      - "8888:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - linghua-network
    depends_on:
      db:
        condition: service_healthy
    command: >
      sh -c "
        echo '等待数据库启动...' &&
        sleep 10 &&
        echo '生成Prisma客户端...' &&
        npx prisma generate &&
        echo '启动开发服务器...' &&
        npm run dev
      "

# 网络配置
networks:
  linghua-network:
    driver: bridge

# 数据卷配置
volumes:
  postgres_data:
    driver: local