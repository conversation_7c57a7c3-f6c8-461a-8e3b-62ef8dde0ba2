/**
 * API缓存优化系统 - 专门解决开发环境API响应慢的问题
 * 
 * 针对2.94秒的API响应时间进行优化
 */

import { NextRequest, NextResponse } from 'next/server';

// 内存缓存存储
const cache = new Map<string, {
  data: any;
  timestamp: number;
  ttl: number;
}>();

// 缓存配置
export const CACHE_CONFIG = {
  // 产品相关API - 缓存5分钟
  products: 5 * 60 * 1000,
  'product-categories': 10 * 60 * 1000,
  
  // 库存相关API - 缓存2分钟（变化较频繁）
  inventory: 2 * 60 * 1000,
  
  // 用户和权限 - 缓存15分钟
  users: 15 * 60 * 1000,
  roles: 15 * 60 * 1000,
  permissions: 15 * 60 * 1000,
  
  // 系统配置 - 缓存30分钟
  settings: 30 * 60 * 1000,
  dictionaries: 30 * 60 * 1000,
  
  // 默认缓存时间
  default: 5 * 60 * 1000,
};

/**
 * 生成缓存键
 */
function generateCacheKey(request: NextRequest): string {
  const url = new URL(request.url);
  const path = url.pathname;
  const searchParams = url.searchParams.toString();
  const method = request.method;
  
  return `${method}:${path}${searchParams ? `?${searchParams}` : ''}`;
}

/**
 * 获取API路径对应的缓存时间
 */
function getCacheTTL(path: string): number {
  // 提取API路径
  const apiPath = path.replace('/api/', '').split('/')[0];
  
  return CACHE_CONFIG[apiPath as keyof typeof CACHE_CONFIG] || CACHE_CONFIG.default;
}

/**
 * 检查缓存是否有效
 */
function isCacheValid(item: { timestamp: number; ttl: number }): boolean {
  return Date.now() - item.timestamp < item.ttl;
}

/**
 * 从缓存获取数据
 */
export function getFromCache(request: NextRequest): any | null {
  const key = generateCacheKey(request);
  const item = cache.get(key);
  
  if (!item || !isCacheValid(item)) {
    if (item) {
      cache.delete(key); // 清理过期缓存
    }
    return null;
  }
  
  return item.data;
}

/**
 * 设置缓存数据
 */
export function setCache(request: NextRequest, data: any): void {
  const key = generateCacheKey(request);
  const ttl = getCacheTTL(new URL(request.url).pathname);
  
  cache.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
  });
  
  // 🧹 缓存清理 - 防止内存泄漏
  if (cache.size > 1000) {
    cleanupCache();
  }
}

/**
 * 清理过期缓存
 */
export function cleanupCache(): void {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [key, item] of cache.entries()) {
    if (!isCacheValid(item)) {
      cache.delete(key);
      cleanedCount++;
    }
  }
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`🧹 [缓存清理] 清理了 ${cleanedCount} 个过期缓存项，当前缓存大小: ${cache.size}`);
  }
}

/**
 * 清除特定API的缓存
 */
export function invalidateCache(pattern: string): void {
  let removedCount = 0;
  
  for (const [key] of cache.entries()) {
    if (key.includes(pattern)) {
      cache.delete(key);
      removedCount++;
    }
  }
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`🗑️ [缓存失效] 清除了 ${removedCount} 个包含 "${pattern}" 的缓存项`);
  }
}

/**
 * API缓存中间件
 */
export function withCache<T>(
  handler: (request: NextRequest) => Promise<NextResponse<T>>,
  options: {
    skipCache?: boolean;
    customTTL?: number;
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse<T>> => {
    // 🚀 只对GET请求使用缓存
    if (request.method !== 'GET' || options.skipCache) {
      return handler(request);
    }
    
    // 🎯 检查缓存
    const cachedData = getFromCache(request);
    if (cachedData) {
      if (process.env.NODE_ENV === 'development') {
        console.log(`🎯 [缓存命中] ${generateCacheKey(request)}`);
      }
      
      // 返回缓存的响应
      return NextResponse.json(cachedData, {
        headers: {
          'X-Cache': 'HIT',
          'X-Cache-Time': new Date().toISOString(),
        },
      });
    }
    
    // 🔥 执行原始处理器
    const startTime = performance.now();
    const response = await handler(request);
    const endTime = performance.now();
    
    // 🚀 缓存响应数据
    if (response.status === 200) {
      try {
        const responseData = await response.clone().json();
        setCache(request, responseData);
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`💾 [缓存设置] ${generateCacheKey(request)} - 响应时间: ${(endTime - startTime).toFixed(2)}ms`);
        }
      } catch (error) {
        console.warn('缓存设置失败:', error);
      }
    }
    
    // 添加缓存相关头信息
    const newResponse = new NextResponse(response.body, {
      status: response.status,
      headers: {
        ...Object.fromEntries(response.headers.entries()),
        'X-Cache': 'MISS',
        'X-Response-Time': `${(endTime - startTime).toFixed(2)}ms`,
      },
    });
    
    return newResponse;
  };
}

/**
 * 缓存统计信息
 */
export function getCacheStats() {
  const stats = {
    totalItems: cache.size,
    validItems: 0,
    expiredItems: 0,
    memoryUsage: 0,
  };
  
  for (const [key, item] of cache.entries()) {
    if (isCacheValid(item)) {
      stats.validItems++;
    } else {
      stats.expiredItems++;
    }
    
    // 估算内存使用（简单计算）
    stats.memoryUsage += JSON.stringify(item).length;
  }
  
  return stats;
}

/**
 * 定期清理任务 - 每10分钟执行一次
 */
if (typeof globalThis !== 'undefined') {
  setInterval(cleanupCache, 10 * 60 * 1000);
}

// 开发环境调试信息
if (process.env.NODE_ENV === 'development') {
  console.log(`
🚀 API缓存系统已启动
📊 缓存配置：
   - 产品API: ${CACHE_CONFIG.products / 1000}秒
   - 库存API: ${CACHE_CONFIG.inventory / 1000}秒  
   - 用户API: ${CACHE_CONFIG.users / 1000}秒
   - 默认: ${CACHE_CONFIG.default / 1000}秒

💡 使用方法：
   export default withCache(async (request) => {
     // 你的API处理逻辑
   });
  `);
}

export default {
  withCache,
  getFromCache,
  setCache,
  invalidateCache,
  cleanupCache,
  getCacheStats,
};