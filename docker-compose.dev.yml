# 开发环境专用配置

services:
  # 开发环境数据库
  linghua-erp-db-dev:
    image: postgres:15-alpine
    container_name: linghua-erp-db-dev
    environment:
      POSTGRES_DB: linghua_enamel_gallery
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5555:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - erp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 开发环境应用 - 性能优化版本
  linghua-erp-app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: linghua-erp-app-dev
    ports:
      - "8888:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************************/linghua_enamel_gallery?schema=public
      - NEXTAUTH_SECRET=linghua-enamel-gallery-secret-key-2024
      - NEXTAUTH_URL=http://localhost:3001
      - AUTH_TRUST_HOST=true
      - AUTH_SECRET=linghua-enamel-gallery-secret-key-2024
      # 🚀 性能优化环境变量
      - NEXTAUTH_DEBUG=false
      - LOG_LEVEL=warn
      - NODE_OPTIONS=--max-old-space-size=4096
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    networks:
      - erp_network
    depends_on:
      linghua-erp-db-dev:
        condition: service_healthy
    develop:
      watch:
        # 🚀 优化文件监听 - 只监听关键文件
        - action: sync
          path: ./app
          target: /app/app
          ignore:
            - "**/*.test.ts"
            - "**/*.test.tsx" 
        - action: sync
          path: ./components
          target: /app/components
        - action: sync
          path: ./lib
          target: /app/lib
        - action: rebuild
          path: package.json

  # Redis缓存 - 用于开发环境性能优化
  linghua-redis-dev:
    image: redis:7-alpine
    container_name: linghua-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - erp_network
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  erp_network:
    driver: bridge