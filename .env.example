# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5555/linghua?schema=public"

# 认证配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# 邮件配置
EMAIL_SERVER_HOST="smtp.example.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-email-password"
EMAIL_FROM="<EMAIL>"

# 应用配置
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="灵华珐琅馆管理系统"
NEXT_PUBLIC_APP_VERSION="1.0.0"

# Sentry 配置
NEXT_PUBLIC_SENTRY_DSN="https://your-sentry-dsn.ingest.sentry.io/project-id"
NEXT_PUBLIC_SENTRY_ENABLED="true"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"
SENTRY_PROJECT="your-sentry-project"
SENTRY_ORG="your-sentry-org"

# 环境配置
NODE_ENV="development" # development, production, test
